import { cn } from '@/utilities/ui'
import React from 'react'
import RichText from '@/components/RichText'

import type { AbraxaCommandBlock as AbraxaCommandBlockProps } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Media } from '@/components/Media'

export const AbraxaCommandBlock: React.FC<AbraxaCommandBlockProps> = (props) => {
  const { columns, title, media } = props

  const colsSpanClasses = {
    full: '12',
    half: '6',
    oneFourth: '3',
    oneThird: '4',
    twoThirds: '8',
  }

  return (
    <div className="container py-20">
      <div className="grid grid-cols-4 lg:grid-cols-12 gap-x-8">
        <div className="col-span-4 lg:col-span-8 text-center lg:col-start-3 md:col-span-2">
          {title && <RichText data={title} enableGutter={false} />}
        </div>
        <div className="col-span-12">
          {media && typeof media === 'object' && (
            <Media imgClassName="mt-20" priority resource={media} />
          )}
        </div>
        {columns &&
          columns.length > 0 &&
          columns.map((col, index) => {
            const { enableLink, link, richText, size, media, badge, className } = col

            return (
              <div
                className={cn(`col-span-4 lg:col-span-${colsSpanClasses[size!]}`, className, {
                  'md:col-span-2': size !== 'full',
                })}
                key={index}
              >
                <div className="AbraxaCard grid gap-y-10 p-10 rounded-3xl">
                  <div className="AbraxaBadge">
                    {badge && typeof badge === 'object' && (
                      <Media imgClassName="size-14" priority resource={badge} />
                    )}
                  </div>

                  {richText && <RichText data={richText} enableGutter={false} />}

                  {media && typeof media === 'object' && (
                    <Media imgClassName="" priority resource={media} />
                  )}

                  {enableLink && <CMSLink {...link} />}
                </div>
              </div>
            )
          })}
      </div>
    </div>
  )
}
