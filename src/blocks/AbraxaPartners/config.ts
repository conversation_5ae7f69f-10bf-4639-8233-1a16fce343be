import type { Block, Field } from 'payload'

import {
  FixedToolbarFeature,
  HeadingFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'

const columnFields: Field[] = [
  {
    name: 'size',
    type: 'select',
    defaultValue: 'oneFourth',
    options: [
      {
        label: 'One Fourth',
        value: 'oneFourth',
      },
      {
        label: 'One Third',
        value: 'oneThird',
      },
      {
        label: 'Half',
        value: 'half',
      },
      {
        label: 'Two Thirds',
        value: 'twoThirds',
      },
      {
        label: 'Full',
        value: 'full',
      },
    ],
  },
  {
    name: 'richText',
    type: 'richText',
    editor: lexicalEditor({
      features: ({ rootFeatures }) => {
        return [
          ...rootFeatures,
          HeadingFeature({ enabledHeadingSizes: ['h2', 'h3', 'h4'] }),
          FixedToolbarFeature(),
          InlineToolbarFeature(),
        ]
      },
    }),
    label: false,
  },
  {
    name: 'media',
    type: 'upload',
    relationTo: 'media',
    required: false,
  },
  {
    name: 'media1',
    type: 'upload',
    relationTo: 'media',
    required: false,
  },
  {
    name: 'media2',
    type: 'upload',
    relationTo: 'media',
    required: false,
  },
  {
    name: 'media3',
    type: 'upload',
    relationTo: 'media',
    required: false,
  },
  {
    name: 'className',
    label: 'Class',
    type: 'text',
    required: false,
  },
]

export const AbraxaPartners: Block = {
  slug: 'abraxaPartners',
  interfaceName: 'AbraxaPartnersBlock',
  fields: [
    {
      name: 'columns',
      type: 'array',
      admin: {
        initCollapsed: true,
      },
      fields: columnFields,
    },
  ],
}
