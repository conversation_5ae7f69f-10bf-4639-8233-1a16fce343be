import { cn } from '@/utilities/ui'
import React from 'react'
import RichText from '@/components/RichText'

import type { AbraxaPartnersBlock as AbraxaPartnersBlockProps } from '@/payload-types'

import { Media } from '@/components/Media'

export const AbraxaPartnersBlock: React.FC<AbraxaPartnersBlockProps> = ({
  richText,
  media,
  media1,
  media2,
  media3,
  className,
}) => {
  return (
    <div className={cn('container py-20', className)}>
      <div className="grid grid-cols-4 lg:grid-cols-12 gap-y-8 gap-x-8">
        <div className="col-span-4">
          {richText && <RichText data={richText} enableGutter={false} />}
        </div>
        <div className="col-span-7 col-end-13">
          <div className="flex justify-between gap-8">
            {media && typeof media === 'object' && (
              <Media imgClassName="" priority resource={media} />
            )}

            {media1 && typeof media1 === 'object' && (
              <Media imgClassName="" priority resource={media1} />
            )}

            {media2 && typeof media2 === 'object' && (
              <Media imgClassName="" priority resource={media2} />
            )}

            {media3 && typeof media3 === 'object' && (
              <Media imgClassName="" priority resource={media3} />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
