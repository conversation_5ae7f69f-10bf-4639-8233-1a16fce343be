import { cn } from '@/utilities/ui'
import React from 'react'
import RichText from '@/components/RichText'

import type { ContentBlock as ContentBlockProps } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Media } from '@/components/Media'

export const ContentBlock: React.FC<ContentBlockProps> = (props) => {
  const { columns } = props

  const colsSpanClasses = {
    full: '12',
    half: '6',
    oneFourth: '3',
    oneThird: '4',
    twoThirds: '8',
  }

  return (
    <div className="container">
      <div className="grid grid-cols-4 lg:grid-cols-12 gap-y-8 gap-x-8">
        {columns &&
          columns.length > 0 &&
          columns.map((col, index) => {
            const { enableLink, enableCenter, link, richText, size, media, media1, className } = col

            return enableCenter ? (
              <div className="col-span-12 flex justify-center" key={index}>
                <div className={cn(`text-center lg:col-span-${colsSpanClasses[size!]}`)}>
                  {richText && <RichText data={richText} enableGutter={true} />}

                  {media && typeof media === 'object' && (
                    <Media imgClassName="" priority resource={media} />
                  )}

                  {media1 && typeof media1 === 'object' && (
                    <Media imgClassName="" priority resource={media1} />
                  )}

                  {enableLink && <CMSLink {...link} />}
                </div>
              </div>
            ) : (
              <div
                className={cn(`col-span-4 lg:col-span-${colsSpanClasses[size!]}`, className, {
                  'md:col-span-2': size !== 'full',
                })}
                key={index}
              >
                {richText && <RichText data={richText} enableGutter={false} />}

                <div className="flex justify-between gap-8">
                  {media && typeof media === 'object' && (
                    <Media imgClassName="" priority resource={media} />
                  )}

                  {media1 && typeof media1 === 'object' && (
                    <Media imgClassName="" priority resource={media1} />
                  )}
                </div>

                {enableLink && <CMSLink {...link} />}
              </div>
            )
          })}
      </div>
    </div>
  )
}
