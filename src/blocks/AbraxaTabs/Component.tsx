'use client'

import { cn } from '@/utilities/ui'
import React, { useState } from 'react'
import RichText from '@/components/RichText'

import type { AbraxaTabsBlock as AbraxaTabsBlockProps } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Media } from '@/components/Media'

export const AbraxaTabsBlock: React.FC<AbraxaTabsBlockProps> = (props) => {
  const { tabs } = props
  const [activeTab, setActiveTab] = useState(0)

  const colsSpanClasses = {
    full: '12',
    half: '6',
    oneFourth: '3',
    oneThird: '4',
    twoThirds: '8',
  }

  if (!tabs || tabs.length === 0) {
    return null
  }

  return (
    <div className="container py-20">
      {/* Tab Navigation */}
      <div className="flex flex-wrap gap-2 mb-8 border-b border-gray-200">
        {tabs.map((tab, index) => (
          <button
            key={index}
            onClick={() => setActiveTab(index)}
            className={cn(
              'px-6 py-3 font-medium text-sm rounded-t-lg transition-colors duration-200',
              'hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
              {
                'bg-white border-b-2 border-blue-500 text-blue-600': activeTab === index,
                'text-gray-500 hover:text-gray-700': activeTab !== index,
              }
            )}
          >
            {tab.tabName}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {tabs[activeTab] && tabs[activeTab].columns && (
          <div className="grid grid-cols-4 lg:grid-cols-12 gap-y-8 gap-x-8">
            {tabs[activeTab].columns.map((col, index) => {
              const { enableLink, enableCenter, link, richText, size, media, media1, className } = col

              return (
                <div
                  className={cn(
                    `col-span-4 lg:col-span-${colsSpanClasses[size!]}`,
                    className,
                    {
                      'md:col-span-2': size !== 'full',
                      'text-center': enableCenter,
                    }
                  )}
                  key={index}
                >
                  {richText && <RichText data={richText} enableGutter={false} />}

                  <div className="flex justify-between gap-8">
                    {media && typeof media === 'object' && (
                      <Media imgClassName="" priority resource={media} />
                    )}

                    {media1 && typeof media1 === 'object' && (
                      <Media imgClassName="" priority resource={media1} />
                    )}
                  </div>

                  {enableLink && <CMSLink {...link} />}
                </div>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}
