'use client'

import React from 'react'

import type { Header as HeaderType } from '@/payload-types'

import { CMSLink } from '@/components/Link'

export const HeaderNav: React.FC<{ data: HeaderType }> = ({ data }) => {
  const navItems = data?.navItems || []

  return (
    <nav className="flex gap-3 items-center">
      {navItems.map(({ link }, i) => {
        return <CMSLink key={i} {...link} appearance="ghost" className="text-base font-normal" />
      })}
      <CMSLink
        appearance="outline"
        label="Log In"
        newTab={true}
        reference={null}
        size="default"
        type="custom"
        url="https://my.abraxa.com/login"
      />
      <CMSLink
        appearance="default"
        label="Get in touch"
        newTab={false}
        reference={null}
        size="default"
        type="custom"
        url="/contact"
      />
    </nav>
  )
}
