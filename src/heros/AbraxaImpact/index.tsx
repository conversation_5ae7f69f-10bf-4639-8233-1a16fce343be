'use client'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import React, { useEffect } from 'react'

import type { Page } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Media } from '@/components/Media'
import RichText from '@/components/RichText'

export const AbraxaImpactHero: React.FC<Page['hero']> = ({ links, media, richText }) => {
  const { setHeaderTheme } = useHeaderTheme()

  useEffect(() => {
    setHeaderTheme('dark')
  })

  return (
    <div className="container pt-28 pb-20" data-theme="dark">
      <div className="grid grid-cols-4 lg:grid-cols-12 gap-y-8 gap-x-8">
        <div className="col-span-4 lg:col-span-5">
          <div className="text-2xl">
            {richText && <RichText data={richText} enableGutter={false} />}
          </div>
          {Array.isArray(links) && links.length > 0 && (
            <ul className="flex gap-4">
              {links.map(({ link }, i) => {
                return (
                  <li key={i}>
                    <CMSLink size="lg" {...link} />
                  </li>
                )
              })}
            </ul>
          )}
        </div>
        <div className="col-span-4 lg:col-span-7 opi">
          {media && typeof media === 'object' && (
            <Media imgClassName="" priority resource={media} />
          )}
        </div>
      </div>
    </div>
  )
}
