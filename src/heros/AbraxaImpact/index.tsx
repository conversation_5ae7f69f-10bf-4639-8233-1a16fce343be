'use client'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import React, { useEffect } from 'react'

import type { Page } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Media } from '@/components/Media'
import RichText from '@/components/RichText'

export const AbraxaImpactHero: React.FC<Page['hero']> = ({ links, media, richText }) => {
  const { setHeaderTheme } = useHeaderTheme()

  useEffect(() => {
    setHeaderTheme('dark')
  })

  return (
    <div className="block-abraxaHero" data-theme="dark">
      <div className="container pt-28">
        <div className="grid grid-cols-4 lg:grid-cols-12 gap-y-8 gap-x-8">
          <div className="col-span-4 lg:col-span-5">
            <div className="text-2xl">
              {richText && <RichText data={richText} enableGutter={false} />}
            </div>
            {Array.isArray(links) && links.length > 0 && (
              <ul className="flex gap-4">
                {links.map(({ link }, i) => {
                  return (
                    <li key={i}>
                      <CMSLink size="lg" {...link} />
                    </li>
                  )
                })}
              </ul>
            )}
          </div>
          <div className="col-span-4 lg:col-span-7 gradient-primary">
            {media && typeof media === 'object' && (
              <Media
                fill
                className="relative h-full w-full"
                imgClassName=""
                priority
                resource={media}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
